const { timeStamp } = require('console')
const crypto = require('crypto')
class Blockchain{
    constructor(){
        this.Blockchain=[]
        this.data=[]
        this.difficulty=4
        const hash = this.computeHash(0,'0',new Date().getTime(),'Hello gbhblockchain!',1)
        console.log(hash)
    }
    mine(){

    }
    generateNewBlock(){

    }
    computeHash(){

    }
    isValidBlock(){

    }
    isValidChain(){

    }

}

function  sha256(str){
    const hash = crypto.createHash('sha256')
    hash.update(str)
    return hash.digest('hex')
}  

console.log(sha256('hello world'))
computeHash(index,performServerHandshake,timestamp,data,nonce){
    return crypto
                .createHash('sha256')
                .update(index+prevHash+timestamp+data+nonce)
                .digest('hex')
}
let bc = new Blockchain()
